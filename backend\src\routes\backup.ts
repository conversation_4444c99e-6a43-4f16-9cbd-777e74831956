import { FastifyInstance } from 'fastify'
import { z } from 'zod'
import { exportServer, validateBackup, importServer } from '../controllers/backup'
import { verifyJWT } from '../middlewares/auth'

// Schema para validação de dados de backup
const backupDataSchema = z.object({
  version: z.string(),
  exportedAt: z.string(),
  exportedBy: z.object({
    id: z.string(),
    name: z.string(),
    email: z.string()
  }),
  server: z.object({
    name: z.string(),
    host: z.string(),
    port: z.number(),
    username: z.string(),
    password: z.string().optional(),
    privateKey: z.string().optional(),
    os: z.enum(['LINUX', 'WINDOWS']),
    deviceType: z.enum(['NOKIA', 'HUAWEI', 'MIKROTIK', 'DMOS', 'GENERIC'])
  }),
  commands: z.array(z.object({
    name: z.string(),
    command: z.string(),
    description: z.string().optional(),
    order: z.number()
  }))
})

const importServerSchema = z.object({
  backupData: backupDataSchema,
  overwriteExisting: z.boolean().optional().default(false),
  newServerName: z.string().optional()
})

const validateBackupSchema = z.object({
  backupData: backupDataSchema
})

export async function backupRoutes(app: FastifyInstance) {
  // Middleware de autenticação para todas as rotas
  app.addHook('onRequest', verifyJWT)

  // Exportar servidor como backup
  app.get('/servers/:id/export', {
    schema: {
      params: z.object({
        id: z.string().uuid()
      }),
      response: {
        200: z.object({
          version: z.string(),
          exportedAt: z.string(),
          exportedBy: z.object({
            id: z.string(),
            name: z.string(),
            email: z.string()
          }),
          server: z.object({
            name: z.string(),
            host: z.string(),
            port: z.number(),
            username: z.string(),
            password: z.string().optional(),
            privateKey: z.string().optional(),
            os: z.enum(['LINUX', 'WINDOWS']),
            deviceType: z.enum(['NOKIA', 'HUAWEI', 'MIKROTIK', 'DMOS', 'GENERIC'])
          }),
          commands: z.array(z.object({
            name: z.string(),
            command: z.string(),
            description: z.string().optional(),
            order: z.number()
          }))
        }),
        404: z.object({
          error: z.string()
        }),
        403: z.object({
          error: z.string()
        })
      }
    }
  }, exportServer)

  // Validar dados de backup
  app.post('/servers/validate-backup', {
    schema: {
      body: validateBackupSchema,
      response: {
        200: z.object({
          isValid: z.boolean(),
          errors: z.array(z.string()),
          warnings: z.array(z.string()),
          serverExists: z.boolean().optional(),
          existingServerId: z.string().optional()
        })
      }
    }
  }, validateBackup)

  // Importar servidor a partir de backup
  app.post('/servers/import', {
    schema: {
      body: importServerSchema,
      response: {
        201: z.object({
          message: z.string(),
          server: z.object({
            id: z.string(),
            name: z.string(),
            host: z.string(),
            port: z.number(),
            username: z.string(),
            os: z.enum(['LINUX', 'WINDOWS']),
            deviceType: z.enum(['NOKIA', 'HUAWEI', 'MIKROTIK', 'DMOS', 'GENERIC']),
            createdAt: z.string(),
            updatedAt: z.string(),
            userId: z.string(),
            commands: z.array(z.object({
              id: z.string(),
              name: z.string(),
              command: z.string(),
              description: z.string().nullable(),
              order: z.number(),
              serverId: z.string(),
              createdAt: z.string(),
              updatedAt: z.string()
            }))
          })
        }),
        400: z.object({
          error: z.string()
        }),
        409: z.object({
          error: z.string()
        })
      }
    }
  }, importServer)
}

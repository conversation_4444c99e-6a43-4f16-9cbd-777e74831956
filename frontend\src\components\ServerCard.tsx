import React, { useState } from 'react'
import { Pencil, Trash2, Terminal, HardDrive, Network, User, Users, FolderPlus, Download } from 'lucide-react'
import { SSHServer, Command } from '../types/server'
import CommandModal from './CommandModal'
import EditServerModal from './EditServerModal'
import ConfirmModal from './ConfirmModal'
import ServerUserAccess from './ServerUserAccess'
import ServerGroupAssignment from './ServerGroupAssignment'
import ServerBackupModal from './ServerBackupModal'
import { api } from '../lib/api'
import { useAuth } from '../contexts/AuthContext'

interface ServerCardProps {
  server: SSHServer & { commands: Command[] }
  onServerUpdated: () => void
}

export function ServerCard({ server, onServerUpdated }: ServerCardProps) {
  const { user: currentUser } = useAuth()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false)
  const [isUserAccessModalOpen, setIsUserAccessModalOpen] = useState(false)
  const [isGroupAssignmentOpen, setIsGroupAssignmentOpen] = useState(false)
  const [isBackupModalOpen, setIsBackupModalOpen] = useState(false)

  // Verificar se o usuário atual é o proprietário do servidor ou um administrador
  const isOwnerOrAdmin = server.userId === currentUser?.id || currentUser?.role === 'ADMIN'

  async function handleDelete() {
    try {
      await api.delete(`/api/servers/${server.id}`)
      onServerUpdated()
    } catch (error) {
      console.error('Erro ao excluir servidor:', error)
      alert('Erro ao excluir servidor. Por favor, tente novamente.')
    }
  }

  return (
    <>
      <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100 hover:border-primary-100 transition-all duration-300">
        <div className="p-4 sm:p-6 space-y-3 sm:space-y-4">
          {/* Cabeçalho com nome e ações */}
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2 sm:gap-3">
              <HardDrive className="h-5 w-5 sm:h-6 sm:w-6 text-primary-500" />
              <h2 className="text-sm sm:text-md font-semibold text-gray-800 tracking-tight">{server.name}</h2>
            </div>
            <div className="flex gap-1 sm:gap-2">
              <button
                onClick={() => setIsBackupModalOpen(true)}
                className="p-1.5 sm:p-2 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                title="Backup/Importação"
              >
                <Download className="h-4 w-4" />
              </button>
              <button
                onClick={() => setIsGroupAssignmentOpen(true)}
                className="p-1.5 sm:p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                title="Gerenciar grupos"
              >
                <FolderPlus className="h-4 w-4" />
              </button>
              {currentUser?.role === 'ADMIN' && (
                <button
                  onClick={() => setIsUserAccessModalOpen(true)}
                  className="p-1.5 sm:p-2 text-gray-500 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors"
                  title="Gerenciar acesso de usuários"
                >
                  <Users className="h-4 w-4" />
                </button>
              )}
              {isOwnerOrAdmin && (
                <>
                  <button
                    onClick={() => setIsEditModalOpen(true)}
                    className="p-1.5 sm:p-2 text-gray-500 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors"
                    title="Editar servidor"
                  >
                    <Pencil className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setIsConfirmDeleteOpen(true)}
                    className="p-1.5 sm:p-2 text-gray-500 hover:text-danger-600 hover:bg-danger-50 rounded-lg transition-colors"
                    title="Excluir servidor"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </>
              )}
            </div>
          </div>

          {/* Informações do servidor */}
          <div className="space-y-2 sm:space-y-3 pt-1 sm:pt-2">
            <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 text-xs sm:text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <Network className="h-4 w-4 text-gray-400" />
                <span className="font-medium">Host:</span>
                <span className="font-mono bg-gray-50 px-2 py-0.5 rounded">{server.host}</span>
              </div>
              <div className="flex items-center gap-2 mt-1 sm:mt-0">
                <span className="font-medium sm:ml-2">Porta:</span>
                <span className="font-mono bg-gray-50 px-2 py-0.5 rounded">{server.port}</span>
              </div>
            </div>

            <div className="flex items-center gap-2 text-xs sm:text-sm text-gray-600">
              <User className="h-4 w-4 text-gray-400" />
              <span className="font-medium">Usuário:</span>
              <span className="font-mono bg-gray-50 px-2 py-0.5 rounded">{server.username}</span>
              <span className="font-medium ml-2">Tipo:</span>
              <span className="bg-primary-50 text-primary-700 px-2 py-0.5 rounded text-xs font-medium">
                SSH
              </span>
            </div>

            {/* Grupos do servidor */}
            {server.groupMembers && server.groupMembers.length > 0 && (
              <div className="flex flex-wrap gap-1 sm:gap-2 mt-2">
                {server.groupMembers.map((member) => (
                  <div
                    key={member.id}
                    className="flex items-center gap-1 px-2 py-1 rounded-full text-xs"
                    style={{
                      backgroundColor: `${member.group.color || '#3B82F6'}20`,
                      color: member.group.color || '#3B82F6'
                    }}
                  >
                    <div
                      className="w-2 h-2 rounded-full"
                      style={{ backgroundColor: member.group.color || '#3B82F6' }}
                    />
                    <span className="font-medium">{member.group.name}</span>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Botão de terminal */}
          <div className="pt-2 sm:pt-4">
            <button
              onClick={() => setIsModalOpen(true)}
              className={`w-full flex items-center justify-center gap-2 px-3 sm:px-4 py-2 sm:py-2.5 text-xs sm:text-sm font-medium rounded-lg transition-colors
                ${server.commands?.length > 0
                  ? 'bg-primary-600 text-white hover:bg-primary-700'
                  : 'bg-gray-200 text-gray-500 cursor-not-allowed'}`}
              title={server.commands?.length > 0 ? 'Abrir terminal' : 'Servidor sem comandos cadastrados'}
              disabled={!server.commands?.length}
            >
              <Terminal className="h-4 w-4" />
              {server.commands?.length > 0 ? 'Abrir Terminal' : 'Sem Comandos'}
            </button>
          </div>
        </div>
      </div>

      <CommandModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        server={server}
      />

      <EditServerModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        server={server}
        onServerUpdated={onServerUpdated}
      />

      <ConfirmModal
        isOpen={isConfirmDeleteOpen}
        onClose={() => setIsConfirmDeleteOpen(false)}
        onConfirm={handleDelete}
        title="Excluir Servidor"
        message="Tem certeza que deseja excluir este servidor? Esta ação não pode ser desfeita."
      />

      <ServerUserAccess
        isOpen={isUserAccessModalOpen}
        onClose={() => setIsUserAccessModalOpen(false)}
        serverId={server.id}
        serverName={server.name}
      />

      <ServerGroupAssignment
        isOpen={isGroupAssignmentOpen}
        onClose={() => setIsGroupAssignmentOpen(false)}
        server={server}
        onServerUpdated={onServerUpdated}
      />

      <ServerBackupModal
        isOpen={isBackupModalOpen}
        onClose={() => setIsBackupModalOpen(false)}
        server={server}
        onServerUpdated={onServerUpdated}
      />
    </>
  )
}